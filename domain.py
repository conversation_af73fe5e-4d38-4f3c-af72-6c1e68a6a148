import datetime
from typing import List, Set, Optional
import optapy
from optapy import planning_entity, planning_solution
from optapy import planning_variable, planning_id, value_range_provider
from optapy import problem_fact_collection_property, planning_entity_collection_property, planning_score
from optapy.score import HardSoftScore

class Skill:
    SKILLED_NURSING = "SKILLED_NURSING"
    PERSONAL_CARE = "PERSONAL_CARE"
    PHYSICAL_THERAPY = "PHYSICAL_THERAPY"
    OCCUPATIONAL_THERAPY = "OCCUPATIONAL_THERAPY"

class ServiceArea:
    def __init__(self, code: str):
        self.code = code
    def __repr__(self):
        return self.code
    def __eq__(self, other):
        return isinstance(other, ServiceArea) and other.code == self.code
    def __hash__(self):
        return hash(self.code)

class AvailabilitySlot:
    def __init__(self, start: datetime.datetime, end: datetime.datetime):
        self.start = start
        self.end = end
    def contains_range(self, start: datetime.datetime, end: datetime.datetime):
        return self.start <= start and self.end >= end
    def __repr__(self):
        return f"{self.start}-{self.end}"

class Clinician:
    def __init__(self, id_: int, name: str, skills: Set[str],
                 service_areas: Set[ServiceArea],
                 availability: List[AvailabilitySlot],
                 max_daily_visits: int = 5):
        self.id = id_
        self.name = name
        self.skills = skills
        self.service_areas = service_areas
        self.availability = availability
        self.max_daily_visits = max_daily_visits
    def __repr__(self):
        return self.name
    def __eq__(self, other):
        return isinstance(other, Clinician) and other.id == self.id
    def __hash__(self):
        return hash(self.id)

class Dependency:
    def __init__(self, predecessor_id: int, same_clinician_required: bool):
        self.predecessor_id = predecessor_id
        self.same_clinician_required = same_clinician_required
    def __repr__(self):
        flag = "SAME" if self.same_clinician_required else "ANY"
        return f"{self.predecessor_id}({flag})"

@planning_entity
class Visit:
    def __init__(self,
                 id_: int,
                 patient_name: str,
                 required_skill: str,
                 service_area: ServiceArea,
                 window_start: datetime.datetime,
                 window_end: datetime.datetime,
                 duration_minutes: int,
                 fixed_time: bool,
                 dependencies: List[Dependency]):

        self.id = id_
        self.patient_name = patient_name
        self.required_skill = required_skill
        self.service_area = service_area
        self.window_start = window_start
        self.window_end = window_end
        self.duration_minutes = duration_minutes
        self.fixed_time = fixed_time
        self.dependencies = dependencies or []

        self.clinician: Optional[Clinician] = None
        self.start: Optional[datetime.datetime] = window_start if fixed_time else None

    # ---- OptaPy annotations ----
    @planning_id
    def get_id(self):
        return self.id

    @planning_variable(Clinician, value_range_provider_refs=['clinicianRange'])
    def get_clinician(self):
        return self.clinician

    def set_clinician(self, value):
        self.clinician = value

    # start time is optional planning variable when not fixed
    @planning_variable(datetime.datetime, value_range_provider_refs=['timeRange'])
    def get_start(self):
        return self.start

    def set_start(self, value):
        self.start = value

    def get_end(self):
        return self.start + datetime.timedelta(minutes=self.duration_minutes) if self.start else None

    # pinning filter via method
    def is_pinned(self):
        return self.fixed_time

    def __repr__(self):
        return f"{self.patient_name}@{self.start}"

@planning_solution
class Schedule:
    def __init__(self,
                 clinicians: List[Clinician],
                 visits: List[Visit],
                 time_range: List[datetime.datetime]):

        self.clinicians = clinicians
        self.visits = visits
        self.time_range = time_range
        self.score: HardSoftScore = None

    @problem_fact_collection_property(Clinician)
    @value_range_provider('clinicianRange')
    def get_clinicians(self):
        return self.clinicians

    @planning_entity_collection_property(Visit)
    def get_visits(self):
        return self.visits

    @problem_fact_collection_property(datetime.datetime)
    @value_range_provider('timeRange')
    def get_time_range(self):
        return self.time_range

    @planning_score(HardSoftScore)
    def get_score(self):
        return self.score

    def set_score(self, score):
        self.score = score
